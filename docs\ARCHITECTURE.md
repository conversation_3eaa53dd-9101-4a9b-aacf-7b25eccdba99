# GuiXiaoXiRag 架构文档

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        UI[Web界面]
        API_CLIENT[API客户端]
        CLI[命令行工具]
    end
    
    subgraph "API网关层"
        FASTAPI[FastAPI服务器]
        MIDDLEWARE[中间件层]
    end
    
    subgraph "业务逻辑层"
        QUERY[查询处理器]
        DOC[文档处理器]
        KB[知识库管理器]
        RAG[RAG服务]
    end
    
    subgraph "数据存储层"
        VECTOR[向量数据库]
        GRAPH[图数据库]
        FILES[文件存储]
        CACHE[缓存层]
    end
    
    subgraph "外部服务"
        OPENAI[OpenAI API]
        LLM[大语言模型]
        EMBED[嵌入模型]
    end
    
    UI --> FASTAPI
    API_CLIENT --> FASTAPI
    CLI --> FASTAPI
    
    FASTAPI --> MIDDLEWARE
    MIDDLEWARE --> QUERY
    MIDDLEWARE --> DOC
    MIDDLEWARE --> KB
    
    QUERY --> RAG
    DOC --> RAG
    KB --> RAG
    
    RAG --> VECTOR
    RAG --> GRAPH
    RAG --> FILES
    RAG --> CACHE
    
    RAG --> OPENAI
    OPENAI --> LLM
    OPENAI --> EMBED
```

### 分层架构

```mermaid
graph TD
    subgraph "表示层 (Presentation Layer)"
        A1[API路由]
        A2[请求验证]
        A3[响应格式化]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        B1[查询处理]
        B2[文档处理]
        B3[知识库管理]
        B4[图谱构建]
    end
    
    subgraph "服务层 (Service Layer)"
        C1[RAG服务]
        C2[向量检索]
        C3[图谱查询]
        C4[LLM调用]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        D1[向量存储]
        D2[图数据存储]
        D3[文件存储]
        D4[缓存存储]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
```

## 🔧 核心组件

### 1. API层组件

#### 路由器 (Routers)
- **query_router.py**: 查询相关路由
- **document_router.py**: 文档管理路由
- **knowledge_base_router.py**: 知识库管理路由
- **knowledge_graph_router.py**: 知识图谱路由
- **system_router.py**: 系统管理路由

#### API处理器 (API Handlers)
- **query_api.py**: 查询API实现
- **document_api.py**: 文档API实现
- **knowledge_base_api.py**: 知识库API实现
- **knowledge_graph_api.py**: 知识图谱API实现
- **system_api.py**: 系统API实现

### 2. 业务逻辑层组件

#### 核心服务
- **guixiaoxirag_service.py**: 核心RAG服务
- **query_processor.py**: 查询处理器
- **document_processor.py**: 文档处理器
- **knowledge_base_manager.py**: 知识库管理器

### 3. 中间件组件

#### 功能中间件
- **logging_middleware.py**: 日志记录中间件
- **metrics_middleware.py**: 性能监控中间件
- **security_middleware.py**: 安全中间件
- **cors_middleware.py**: CORS中间件

### 4. 数据模型组件

#### 模型定义
- **query_models.py**: 查询相关模型
- **document_models.py**: 文档相关模型
- **response_models.py**: 响应模型
- **request_models.py**: 请求模型
- **system_models.py**: 系统模型

## 🔄 数据流

### 查询流程

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Middleware
    participant QueryProcessor
    participant RAGService
    participant VectorDB
    participant GraphDB
    participant LLM
    
    Client->>API: 发送查询请求
    API->>Middleware: 请求预处理
    Middleware->>QueryProcessor: 查询处理
    QueryProcessor->>RAGService: 调用RAG服务
    
    RAGService->>VectorDB: 向量检索
    VectorDB-->>RAGService: 返回相关文档
    
    RAGService->>GraphDB: 图谱查询
    GraphDB-->>RAGService: 返回实体关系
    
    RAGService->>LLM: 生成回答
    LLM-->>RAGService: 返回回答
    
    RAGService-->>QueryProcessor: 返回结果
    QueryProcessor-->>Middleware: 返回处理结果
    Middleware-->>API: 返回响应
    API-->>Client: 返回最终结果
```

### 文档处理流程

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant DocumentProcessor
    participant RAGService
    participant VectorDB
    participant GraphDB
    participant FileStorage
    
    Client->>API: 上传文档
    API->>DocumentProcessor: 处理文档
    DocumentProcessor->>FileStorage: 保存原文件
    
    DocumentProcessor->>RAGService: 文档解析
    RAGService->>RAGService: 文本分块
    RAGService->>RAGService: 实体提取
    RAGService->>RAGService: 关系挖掘
    
    RAGService->>VectorDB: 存储向量
    RAGService->>GraphDB: 存储图谱
    
    RAGService-->>DocumentProcessor: 处理完成
    DocumentProcessor-->>API: 返回结果
    API-->>Client: 返回处理状态
```

## 🗄️ 数据存储

### 存储架构

```mermaid
graph TB
    subgraph "文件存储"
        F1[原始文档]
        F2[处理日志]
        F3[配置文件]
    end
    
    subgraph "向量存储"
        V1[文档向量]
        V2[实体向量]
        V3[关系向量]
    end
    
    subgraph "图存储"
        G1[实体节点]
        G2[关系边]
        G3[图结构]
    end
    
    subgraph "缓存存储"
        C1[查询缓存]
        C2[LLM缓存]
        C3[向量缓存]
    end
    
    F1 --> V1
    V1 --> G1
    G1 --> C1
```

### 数据模型

#### 向量数据模型
```python
{
    "id": "unique_id",
    "content": "文本内容",
    "vector": [0.1, 0.2, ...],
    "metadata": {
        "source": "文档来源",
        "type": "entity|relation|chunk",
        "timestamp": "创建时间"
    }
}
```

#### 图数据模型
```python
{
    "nodes": [
        {
            "id": "entity_id",
            "label": "实体名称",
            "type": "实体类型",
            "properties": {...}
        }
    ],
    "edges": [
        {
            "source": "source_id",
            "target": "target_id",
            "relation": "关系类型",
            "properties": {...}
        }
    ]
}
```

## 🔧 配置管理

### 配置层次

```mermaid
graph TD
    A[环境变量] --> B[配置文件]
    B --> C[默认配置]
    C --> D[运行时配置]
    
    D --> E[服务器配置]
    D --> F[数据库配置]
    D --> G[API配置]
    D --> H[日志配置]
```

### 配置文件结构
```
config/
├── config.example.py    # 配置模板
├── config.py           # 实际配置（不提交到版本控制）
├── development.py      # 开发环境配置
├── production.py       # 生产环境配置
└── testing.py          # 测试环境配置
```

## 🚀 部署架构

### 单机部署

```mermaid
graph TB
    subgraph "服务器"
        subgraph "应用容器"
            APP[GuiXiaoXiRag应用]
            NGINX[Nginx反向代理]
        end
        
        subgraph "数据存储"
            DATA[数据目录]
            LOGS[日志目录]
        end
    end
    
    NGINX --> APP
    APP --> DATA
    APP --> LOGS
```

### 集群部署

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器]
    end
    
    subgraph "应用层"
        APP1[应用实例1]
        APP2[应用实例2]
        APP3[应用实例3]
    end
    
    subgraph "存储层"
        SHARED[共享存储]
        CACHE[分布式缓存]
    end
    
    LB --> APP1
    LB --> APP2
    LB --> APP3
    
    APP1 --> SHARED
    APP2 --> SHARED
    APP3 --> SHARED
    
    APP1 --> CACHE
    APP2 --> CACHE
    APP3 --> CACHE
```

## 📊 性能优化

### 缓存策略

```mermaid
graph TD
    A[请求] --> B{缓存检查}
    B -->|命中| C[返回缓存结果]
    B -->|未命中| D[处理请求]
    D --> E[更新缓存]
    E --> F[返回结果]
    
    subgraph "缓存层次"
        L1[L1: 内存缓存]
        L2[L2: 本地文件缓存]
        L3[L3: 分布式缓存]
    end
```

### 查询优化

```mermaid
graph TD
    A[查询请求] --> B[查询分析]
    B --> C{查询类型}
    
    C -->|简单查询| D[直接检索]
    C -->|复杂查询| E[查询分解]
    C -->|混合查询| F[多路检索]
    
    D --> G[结果合并]
    E --> G
    F --> G
    
    G --> H[结果排序]
    H --> I[返回结果]
```

## 🔒 安全架构

### 安全层次

```mermaid
graph TD
    A[网络安全] --> B[应用安全]
    B --> C[数据安全]
    C --> D[访问控制]
    
    A --> A1[HTTPS/TLS]
    A --> A2[防火墙]
    
    B --> B1[输入验证]
    B --> B2[CORS控制]
    
    C --> C1[数据加密]
    C --> C2[敏感信息脱敏]
    
    D --> D1[身份认证]
    D --> D2[权限控制]
```

## 📈 监控架构

### 监控体系

```mermaid
graph TD
    subgraph "应用监控"
        M1[性能指标]
        M2[错误监控]
        M3[业务指标]
    end
    
    subgraph "基础设施监控"
        M4[系统资源]
        M5[网络状态]
        M6[存储状态]
    end
    
    subgraph "日志监控"
        M7[访问日志]
        M8[错误日志]
        M9[业务日志]
    end
    
    M1 --> DASHBOARD[监控面板]
    M2 --> DASHBOARD
    M3 --> DASHBOARD
    M4 --> DASHBOARD
    M5 --> DASHBOARD
    M6 --> DASHBOARD
    M7 --> DASHBOARD
    M8 --> DASHBOARD
    M9 --> DASHBOARD
```

这个架构文档提供了系统的全面视图，帮助开发者理解系统的设计和实现。
