# GuiXiaoXiRag服务器依赖
# 重构版本 2.0.0

# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# 数据验证和序列化
pydantic>=2.5.0
pydantic-settings>=2.1.0

# HTTP客户端
httpx>=0.25.0
requests>=2.31.0

# 异步支持
asyncio-mqtt>=0.13.0
aiofiles>=23.2.1

# 日志和监控
structlog>=23.2.0
colorlog>=6.8.0

# 数据处理
numpy>=1.24.0
pandas>=2.1.0

# 文件处理
python-docx>=1.1.0
PyPDF2>=3.0.1
openpyxl>=3.1.2
python-magic>=0.4.27

# 文本处理
jieba>=0.42.1
nltk>=3.8.1

# 机器学习和向量
scikit-learn>=1.3.0
sentence-transformers>=2.2.2

# 图数据库和图处理
networkx>=3.2.1
igraph>=0.11.0

# 数据存储
sqlite3  # 内置模块
redis>=5.0.1

# 配置管理
python-dotenv>=1.0.0
toml>=0.10.2

# 安全
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# 性能监控
psutil>=5.9.0
memory-profiler>=0.61.0
