# GuiXiaoXiRag API 文档

## 概述

GuiXiaoXiRag是一个基于知识图谱的智能问答系统，提供RESTful API接口，支持多种查询模式、文档管理、知识库管理等功能。

## 服务信息

- **服务名称**: GuiXiaoXiRag FastAPI Service
- **版本**: 1.0.0
- **基础URL**: `http://localhost:8002`
- **API前缀**: `/api/v1`
- **文档地址**: `http://localhost:8002/docs`

## 认证

当前版本不需要认证，所有API端点都是公开的。

## 响应格式

所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

错误响应格式：
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "path": "/api/v1/endpoint"
}
```

## API 端点

### 1. 基础端点

#### 1.1 根端点
- **URL**: `GET /`
- **描述**: 获取服务基本信息
- **响应示例**:
```json
{
  "service": "GuiXiaoXiRag FastAPI Service",
  "version": "1.0.0",
  "status": "running",
  "docs": "/docs",
  "redoc": "/redoc",
  "health": "/api/v1/health"
}
```

#### 1.2 健康检查
- **URL**: `GET /api/v1/health`
- **描述**: 检查服务健康状态
- **响应示例**:
```json
{
  "success": true,
  "message": "服务运行正常",
  "data": {
    "status": "healthy",
    "timestamp": "2025-08-17T13:00:00Z",
    "uptime": 3600,
    "services": {
      "guixiaoxirag_service": true,
      "knowledge_base_manager": true
    }
  }
}
```

### 2. 查询API

#### 2.1 智能查询
- **URL**: `POST /api/v1/query`
- **描述**: 基于知识图谱的智能查询
- **请求体**:
```json
{
  "query": "什么是人工智能？",
  "mode": "hybrid",
  "language": "中文",
  "knowledge_base": "default",
  "top_k": 10,
  "stream": false
}
```

- **查询模式说明**:
  - `local`: 本地模式 - 专注于上下文相关信息
  - `global`: 全局模式 - 利用全局知识
  - `hybrid`: 混合模式 - 结合本地和全局检索（推荐）
  - `naive`: 朴素模式 - 执行基本搜索
  - `mix`: 混合模式 - 整合知识图谱和向量检索
  - `bypass`: 绕过模式 - 直接返回结果

- **响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "result": "人工智能是...",
    "mode": "hybrid",
    "query": "什么是人工智能？",
    "knowledge_base": "default",
    "language": "中文",
    "context_sources": ["source1", "source2"],
    "confidence": 0.85,
    "response_time": 0.5
  }
}
```

#### 2.2 批量查询
- **URL**: `POST /api/v1/query/batch`
- **描述**: 批量处理多个查询
- **请求体**:
```json
{
  "queries": [
    {"query": "什么是机器学习？", "mode": "hybrid"},
    {"query": "深度学习的应用", "mode": "local"}
  ],
  "language": "中文",
  "knowledge_base": "default"
}
```

#### 2.3 查询意图分析
- **URL**: `POST /api/v1/query/intent-analysis`
- **描述**: 分析查询意图和安全性
- **请求体**:
```json
{
  "query": "如何学习人工智能？",
  "language": "中文"
}
```

### 3. 文档管理API

#### 3.1 文件上传
- **URL**: `POST /api/v1/documents/upload`
- **描述**: 上传并处理文档文件
- **请求**: `multipart/form-data`
- **参数**:
  - `file`: 文档文件（支持PDF、DOCX、TXT等）
  - `knowledge_base`: 目标知识库
  - `language`: 文档语言

#### 3.2 文本插入
- **URL**: `POST /api/v1/documents/insert-text`
- **描述**: 直接插入文本内容
- **请求体**:
```json
{
  "text": "要插入的文本内容",
  "knowledge_base": "default",
  "language": "中文",
  "metadata": {
    "title": "文档标题",
    "source": "来源"
  }
}
```

### 4. 知识库管理API

#### 4.1 获取知识库列表
- **URL**: `GET /api/v1/knowledge-bases`
- **描述**: 获取所有可用的知识库
- **响应示例**:
```json
{
  "success": true,
  "message": "获取知识库列表成功",
  "data": {
    "knowledge_bases": [
      {
        "name": "default",
        "description": "默认知识库",
        "is_current": true,
        "document_count": 10,
        "created_time": "2025-01-01T00:00:00Z"
      }
    ],
    "current_kb": "default",
    "total_count": 5
  }
}
```

#### 4.2 创建知识库
- **URL**: `POST /api/v1/knowledge-bases`
- **描述**: 创建新的知识库
- **请求体**:
```json
{
  "name": "new_kb",
  "description": "新知识库",
  "language": "中文"
}
```

#### 4.3 切换知识库
- **URL**: `POST /api/v1/knowledge-bases/switch`
- **描述**: 切换当前使用的知识库
- **请求体**:
```json
{
  "knowledge_base": "target_kb"
}
```

### 5. 知识图谱API

#### 5.1 获取图谱统计
- **URL**: `GET /api/v1/knowledge-graph/stats`
- **描述**: 获取知识图谱统计信息
- **响应示例**:
```json
{
  "success": true,
  "message": "获取统计信息成功",
  "data": {
    "knowledge_base": "default",
    "working_dir": "./knowledgeBase/default",
    "xml_file_exists": true,
    "json_file_exists": true,
    "node_count": 150,
    "edge_count": 300,
    "file_size": 1024000
  }
}
```

#### 5.2 导出图谱数据
- **URL**: `GET /api/v1/knowledge-graph/export`
- **描述**: 导出知识图谱数据
- **参数**:
  - `format`: 导出格式（json、graphml）

#### 5.3 清空图谱
- **URL**: `DELETE /api/v1/knowledge-graph/clear`
- **描述**: 清空当前知识图谱数据

### 6. 系统管理API

#### 6.1 系统状态
- **URL**: `GET /api/v1/system/status`
- **描述**: 获取系统运行状态
- **响应示例**:
```json
{
  "success": true,
  "message": "获取系统状态成功",
  "data": {
    "service_status": "running",
    "uptime": 3600,
    "memory_usage": "512MB",
    "cpu_usage": "15%",
    "disk_usage": "2GB",
    "active_connections": 5
  }
}
```

#### 6.2 获取日志
- **URL**: `GET /api/v1/system/logs`
- **描述**: 获取系统日志
- **参数**:
  - `lines`: 日志行数（默认100）

#### 6.3 系统重置
- **URL**: `POST /api/v1/system/reset`
- **描述**: 重置系统（谨慎使用）
- **请求体**:
```json
{
  "confirm": true,
  "backup_data": true,
  "reset_config": false
}
```

## 错误代码

| 错误代码 | 描述 | HTTP状态码 |
|---------|------|-----------|
| VALIDATION_ERROR | 请求参数验证失败 | 422 |
| NOT_FOUND | 资源不存在 | 404 |
| INTERNAL_ERROR | 服务器内部错误 | 500 |
| QUERY_FAILED | 查询执行失败 | 500 |
| KB_NOT_FOUND | 知识库不存在 | 404 |
| FILE_TOO_LARGE | 文件过大 | 413 |
| UNSUPPORTED_FORMAT | 不支持的文件格式 | 400 |

## 使用示例

### Python示例
```python
import requests

# 基础配置
BASE_URL = "http://localhost:8002/api/v1"

# 查询示例
def query_example():
    response = requests.post(
        f"{BASE_URL}/query",
        json={
            "query": "什么是人工智能？",
            "mode": "hybrid"
        }
    )
    return response.json()

# 文件上传示例
def upload_file_example():
    with open("document.pdf", "rb") as f:
        files = {"file": f}
        data = {
            "knowledge_base": "default",
            "language": "中文"
        }
        response = requests.post(
            f"{BASE_URL}/documents/upload",
            files=files,
            data=data
        )
    return response.json()
```

### cURL示例
```bash
# 查询示例
curl -X POST http://localhost:8002/api/v1/query \
  -H "Content-Type: application/json" \
  -d '{"query": "什么是人工智能？", "mode": "hybrid"}'

# 获取知识库列表
curl http://localhost:8002/api/v1/knowledge-bases

# 健康检查
curl http://localhost:8002/api/v1/health
```

## 性能建议

1. **查询优化**: 使用`hybrid`模式获得最佳查询效果
2. **批量处理**: 对于多个查询，使用批量查询API提高效率
3. **文件上传**: 大文件建议分块上传或压缩后上传
4. **缓存**: 系统内置查询缓存，相同查询会返回缓存结果
5. **并发**: 支持并发查询，但建议控制并发数量

## 限制说明

- 单个文件上传大小限制：50MB
- 查询文本长度限制：10000字符
- 并发查询数量建议：≤10
- 支持的文件格式：PDF、DOCX、TXT、MD、JSON

## 更新日志

### v1.0.0 (2025-08-17)
- 初始版本发布
- 支持基础查询、文档管理、知识库管理功能
- 提供完整的RESTful API接口
- 支持多种查询模式和文件格式
- 重构代码架构，模块化设计
- 修复QueryParam timeout参数bug
- 优化中间件和初始化流程
