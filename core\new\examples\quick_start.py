#!/usr/bin/env python3
"""
向量化Q&A系统快速开始

展示如何使用向量化问答对存储和检索功能
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# 添加src路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qa_manager import QAManager
from embedding_client import create_embedding_client


async def quick_start_demo():
    """快速开始演示"""
    print("🚀 向量化Q&A系统快速开始")
    print("=" * 60)
    print("功能：问题向量化存储 + 答案文本保存 + 智能检索")
    print("=" * 60)
    
    # 步骤1：创建Q&A管理器
    print("\n📝 步骤1：创建向量化Q&A管理器")
    
    # 尝试使用真实embedding，失败则使用模拟
    try:
        embedding_client = create_embedding_client(use_mock=False)
        connection_ok = await embedding_client.test_connection()
        if connection_ok:
            print("✅ 使用真实embedding模型")
            use_real = True
        else:
            raise Exception("Connection failed")
    except Exception as e:
        print(f"⚠️ 真实embedding连接失败: {e}")
        print("🔧 回退到模拟embedding")
        embedding_client = create_embedding_client(use_mock=True)
        use_real = False
    
    qa_manager = QAManager(
        storage_file="quick_start_qa.json",
        embedding_client=embedding_client,
        similarity_threshold=0.80,
        max_results=3
    )
    
    # 初始化系统
    await qa_manager.initialize()
    print("✅ 向量化Q&A管理器初始化成功")
    if use_real:
        stats = embedding_client.get_statistics()
        print(f"   - 向量维度: {stats['config']['embedding_dim']}")
        print(f"   - 模型: {stats['config']['model']}")
    else:
        print(f"   - 向量维度: 2560 (模拟)")
    print(f"   - 相似度阈值: 0.80")
    print(f"   - 存储文件: quick_start_qa.json")
    
    # 步骤2：添加问答对（问题自动向量化）
    print("\n📝 步骤2：添加问答对（问题自动向量化）")
    
    sample_qa_pairs = [
        {
            "question": "什么是人工智能？",
            "answer": "人工智能（AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的智能机器，如学习、推理、问题解决和决策制定。",
            "category": "AI基础",
            "confidence": 0.98,
            "keywords": ["AI", "人工智能", "机器智能", "计算机科学"]
        },
        {
            "question": "什么是机器学习？",
            "answer": "机器学习是AI的一个子集，它使计算机能够从数据中学习和改进，而无需明确编程。它使用算法来分析数据、识别模式，并基于该数据做出预测或决策。",
            "category": "机器学习",
            "confidence": 0.95,
            "keywords": ["机器学习", "ML", "数据学习", "算法", "模式识别"]
        },
        {
            "question": "什么是深度学习？",
            "answer": "深度学习是机器学习的一个子集，使用具有多层的神经网络（深度神经网络）来建模和理解数据中的复杂模式。它在图像识别、自然语言处理和语音识别等任务中特别有效。",
            "category": "深度学习",
            "confidence": 0.94,
            "keywords": ["深度学习", "神经网络", "多层网络", "模式识别"]
        },
        {
            "question": "什么是自然语言处理？",
            "answer": "自然语言处理（NLP）是AI的一个分支，专注于使计算机能够理解、解释和生成人类语言。包括文本分析、语言翻译、情感分析、问答系统等应用。",
            "category": "NLP",
            "confidence": 0.93,
            "keywords": ["NLP", "自然语言处理", "文本分析", "语言理解"]
        },
        {
            "question": "什么是向量数据库？",
            "answer": "向量数据库专门用于存储和查询高维向量数据，主要作用包括高效的相似性搜索、支持AI应用的向量检索、实现语义搜索、支持推荐系统等。",
            "category": "数据库",
            "confidence": 0.91,
            "keywords": ["向量数据库", "相似性搜索", "语义搜索", "向量检索"]
        }
    ]
    
    print(f"正在添加 {len(sample_qa_pairs)} 个问答对...")
    
    for i, qa_data in enumerate(sample_qa_pairs, 1):
        qa_id = await qa_manager.add_qa_pair(**qa_data)
        if qa_id:
            print(f"   {i}. ✅ 问题向量化并存储: {qa_data['question'][:40]}...")
            print(f"      📝 答案文本已保存: {qa_data['answer'][:50]}...")
            print(f"      🆔 问答对ID: {qa_id}")
        else:
            print(f"   {i}. ❌ 添加失败: {qa_data['question'][:40]}...")
    
    # 步骤3：验证存储格式
    print("\n📝 步骤3：验证存储格式（向量化问题 + 文本答案一同存放）")
    stats = qa_manager.get_statistics()
    print(f"✅ 存储验证:")
    print(f"   - 总问答对数: {stats['storage_stats']['total_pairs']}")
    print(f"   - 向量索引大小: {stats['storage_stats']['vector_index_size']}")
    print(f"   - 向量维度: {stats['storage_stats']['embedding_dim']}")
    print(f"   - 分类分布: {stats['storage_stats']['categories']}")
    print(f"   - 平均置信度: {stats['storage_stats']['average_confidence']}")
    
    # 步骤4：智能检索测试
    print("\n📝 步骤4：智能检索测试（基于向量相似度）")
    
    test_queries = [
        "AI是什么？",
        "机器学习的原理",
        "深度学习技术",
        "NLP的应用",
        "向量数据库的作用",
        "完全不相关的问题"
    ]
    
    print(f"测试 {len(test_queries)} 个查询...")
    
    successful_matches = 0
    total_queries = len(test_queries)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n   查询 {i}: {query}")
        
        # 执行智能检索
        result = await qa_manager.query(query, top_k=2)
        
        if result["success"]:
            if result["found"]:
                successful_matches += 1
                similarity = result["similarity"]
                answer = result["answer"]
                category = result["category"]
                
                print(f"   ✅ 智能匹配成功!")
                print(f"      🎯 相似度: {similarity:.3f}")
                print(f"      🏷️ 分类: {category}")
                print(f"      💬 答案: {answer[:80]}...")
                
                # 显示其他匹配结果
                if len(result["all_results"]) > 1:
                    print(f"      📋 其他相关结果:")
                    for j, r in enumerate(result["all_results"][1:], 1):
                        print(f"         {j+1}. 相似度: {r['similarity']:.3f} - {r['question'][:35]}...")
            else:
                print(f"   ⚪ 未找到匹配 (相似度低于阈值 0.80)")
        else:
            print(f"   ❌ 查询失败: {result.get('error', '未知错误')}")
    
    # 步骤5：性能统计
    print(f"\n📝 步骤5：性能统计")
    final_stats = qa_manager.get_statistics()
    query_performance = final_stats['query_performance']
    
    print(f"✅ 性能报告:")
    print(f"   - 总查询数: {query_performance['total_queries']}")
    print(f"   - 成功查询: {query_performance['successful_queries']}")
    print(f"   - 成功率: {query_performance['success_rate_percent']:.1f}%")
    print(f"   - 平均响应时间: {query_performance['avg_response_time_ms']:.2f}ms")
    
    if 'embedding_stats' in final_stats:
        embedding_stats = final_stats['embedding_stats']['stats']
        print(f"   - Embedding请求: {embedding_stats['total_requests']}")
        print(f"   - 向量化文本: {embedding_stats['total_texts']}")
        if 'avg_time_per_text' in embedding_stats:
            print(f"   - 平均向量化时间: {embedding_stats['avg_time_per_text']*1000:.2f}ms/文本")
    
    # 步骤6：数据持久化验证
    print(f"\n📝 步骤6：数据持久化验证")
    
    # 保存数据
    save_success = await qa_manager.save_storage()
    if save_success:
        print("✅ 数据已保存到磁盘")
        
        # 检查文件是否存在
        if os.path.exists("quick_start_qa.json"):
            file_size = os.path.getsize("quick_start_qa.json")
            print(f"   📁 存储文件: quick_start_qa.json ({file_size} 字节)")
            
            # 显示存储格式示例
            with open("quick_start_qa.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            print(f"   📊 存储格式验证:")
            print(f"      - 元数据: ✅")
            print(f"      - 问答对数据: ✅ ({len(data.get('qa_pairs', []))} 个)")
            print(f"      - 向量数据: ✅ (每个问题都有对应向量)")
            print(f"      - 文本答案: ✅ (原始文本格式保存)")
            print(f"      - 统计信息: ✅")
            
            # 显示一个问答对的存储格式
            if data.get('qa_pairs'):
                sample_qa = data['qa_pairs'][0]
                print(f"   🔍 存储格式示例:")
                print(f"      - 问题: '{sample_qa['question']}'")
                print(f"      - 答案: '{sample_qa['answer'][:50]}...'")
                print(f"      - 向量: [长度 {len(sample_qa.get('question_vector', []))}]")
                print(f"      - 分类: {sample_qa['category']}")
    
    # 清理
    print(f"\n📝 步骤7：清理演示数据")
    await qa_manager.cleanup()
    
    # 删除演示文件
    if os.path.exists("quick_start_qa.json"):
        os.remove("quick_start_qa.json")
        print("✅ 演示数据已清理")
    
    # 总结
    print(f"\n{'='*60}")
    print("🎉 向量化Q&A系统快速开始演示完成！")
    print("=" * 60)
    
    print(f"✅ 已验证核心功能:")
    print(f"   1. 问题自动向量化存储 - ✅ 完成")
    print(f"   2. 答案文本格式保存 - ✅ 完成")
    print(f"   3. 向量化问题和答案一同存放 - ✅ 完成")
    print(f"   4. 基于向量相似度的智能检索 - ✅ 完成")
    print(f"   5. 数据持久化存储 - ✅ 完成")
    
    print(f"\n📊 演示结果:")
    print(f"   - 成功添加: {len(sample_qa_pairs)} 个问答对")
    print(f"   - 查询测试: {total_queries} 次")
    print(f"   - 成功匹配: {successful_matches} 次")
    print(f"   - 匹配率: {successful_matches/total_queries*100:.1f}%")
    if use_real:
        print(f"   - 使用真实embedding模型")
    else:
        print(f"   - 使用模拟embedding")
    
    print(f"\n🚀 系统已准备好用于生产环境！")
    print(f"💡 下一步：")
    if not use_real:
        print(f"   1. 配置真实的embedding服务")
    print(f"   2. 导入您的实际问答数据")
    print(f"   3. 调整相似度阈值以适应业务需求")
    print(f"   4. 集成到现有的问答系统中")


async def main():
    """主函数"""
    try:
        await quick_start_demo()
    except KeyboardInterrupt:
        print(f"\n👋 演示被用户中断")
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
