{"name": "lightrag-webui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bunx --bun vite", "build": "bunx --bun vite build", "lint": "eslint .", "preview": "bunx --bun vite preview", "dev-no-bun": "vite", "build-no-bun": "vite build --emptyOutDir", "preview-no-bun": "vite preview"}, "dependencies": {"@faker-js/faker": "^9.5.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-use-controllable-state": "^1.1.0", "@react-sigma/core": "^5.0.2", "@react-sigma/graph-search": "^5.0.3", "@react-sigma/layout-circlepack": "^5.0.2", "@react-sigma/layout-circular": "^5.0.2", "@react-sigma/layout-force": "^5.0.2", "@react-sigma/layout-forceatlas2": "^5.0.2", "@react-sigma/layout-noverlap": "^5.0.2", "@react-sigma/layout-random": "^5.0.2", "@react-sigma/minimap": "^5.0.2", "@sigma/edge-curve": "^3.1.0", "@sigma/node-border": "^3.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "graphology": "^0.26.0", "graphology-generators": "^0.11.2", "i18next": "^24.2.2", "katex": "^0.16.22", "lucide-react": "^0.475.0", "mermaid": "^11.9.0", "minisearch": "^7.1.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.6", "react-error-boundary": "^5.0.0", "react-i18next": "^15.4.1", "react-markdown": "^9.1.0", "react-number-format": "^5.4.3", "react-router-dom": "^7.3.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-react": "^8.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "seedrandom": "^3.0.5", "sigma": "^3.0.1", "sonner": "^1.7.4", "tailwind-merge": "^3.0.2", "tailwind-scrollbar": "^4.0.1", "typography": "^0.16.24", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@stylistic/eslint-plugin-js": "^3.1.0", "@tailwindcss/vite": "^4.0.8", "@types/bun": "^1.2.3", "@types/katex": "^0.16.7", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-i18next": "^8.1.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/seedrandom": "^3.0.8", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "graphology-types": "^0.24.8", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.8", "tailwindcss-animate": "^1.0.7", "typescript": "~5.7.3", "typescript-eslint": "^8.24.1", "vite": "^6.1.1"}}