[bumpversion]
current_version = 1.6.5
commit = True
tag = True

[bumpversion:file:setup.py]
search = version="{current_version}"
replace = version="{new_version}"

[bumpversion:file:textract/__init__.py]
search = VERSION = "{current_version}"
replace = VERSION = "{new_version}"

[bumpversion:file:docs/conf.py]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:docs/changelog.rst]
search = THANKS FOR CONTRIBUTING; ADD YOUR UNRELEASED CHANGES HERE!
replace = THANKS FOR CONTRIBUTING; ADD YOUR UNRELEASED CHANGES HERE!
	{new_version}
	-------------------

[egg_info]
tag_build = 
tag_date = 0

