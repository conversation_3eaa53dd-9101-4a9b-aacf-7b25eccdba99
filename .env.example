# GuiXiaoXiRag FastAPI 环境配置文件
# 复制此文件为 .env 并根据实际情况修改配置

# ==================== 应用基础配置 ====================
APP_NAME=GuiXiaoXiRag FastAPI Service
APP_VERSION=1.0.0

# ==================== 服务配置 ====================
# FastAPI 服务配置
HOST=0.0.0.0
PORT=8002
DEBUG=false
WORKERS=1

# ==================== GuiXiaoXiRag 配置 ====================
# 知识库工作目录
WORKING_DIR=./knowledgeBase/default

# ==================== 大模型配置 ====================
# OpenAI API 配置 (默认使用本地模型)
OPENAI_API_BASE=http://localhost:8100/v1
OPENAI_EMBEDDING_API_BASE=http://localhost:8200/v1
OPENAI_CHAT_API_KEY=sk-8a2b5c9d-e1f3-4g7h-6i2j-k3l4m5n6o7p8
OPENAI_CHAT_MODEL=qwen14b
OPENAI_EMBEDDING_MODEL=embedding_qwen
OPENAI_EMBEDDING_API_KEY=sk-8a2b5c9d-e1f3-4g7h-6i2j-k3l4m5n6o7p8

# 如果使用官方 OpenAI API，请取消注释并设置以下配置：
# OPENAI_API_BASE=https://api.openai.com/v1
# OPENAI_EMBEDDING_API_BASE=https://api.openai.com/v1
# OPENAI_CHAT_API_KEY=your-openai-api-key-here
# OPENAI_CHAT_MODEL=gpt-4o-mini
# OPENAI_EMBEDDING_MODEL=text-embedding-3-large
# OPENAI_EMBEDDING_API_KEY=your-openai-api-key-here

# ==================== 自定义 LLM 提供商配置 (可选) ====================
# 支持: openai, azure, ollama, anthropic, etc.
# CUSTOM_LLM_PROVIDER=openai
# CUSTOM_EMBEDDING_PROVIDER=openai

# Azure OpenAI 配置 (如果使用 Azure)
# AZURE_API_VERSION=2024-02-15-preview
# AZURE_DEPLOYMENT_NAME=your-deployment-name

# Ollama 配置 (如果使用本地 Ollama)
# OLLAMA_BASE_URL=http://localhost:11434
# OLLAMA_CHAT_MODEL=llama2
# OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# ==================== Embedding 配置 ====================
# 向量维度配置
EMBEDDING_DIM=2560
MAX_TOKEN_SIZE=8192

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_DIR=./logs

# ==================== 文件上传配置 ====================
# 文件大小限制 (字节)
MAX_FILE_SIZE=52428800
# 上传目录
UPLOAD_DIR=./uploads

# ==================== Streamlit 配置 ====================
# Streamlit 服务配置
STREAMLIT_HOST=0.0.0.0
STREAMLIT_PORT=8501
STREAMLIT_DEBUG=false

# Streamlit API 配置
STREAMLIT_API_URL=http://localhost:8002
STREAMLIT_API_TIMEOUT=120
STREAMLIT_API_RETRY_TIMES=3
STREAMLIT_API_RETRY_DELAY=1.0

# Streamlit 界面配置
STREAMLIT_MAX_UPLOAD_SIZE=50
STREAMLIT_ITEMS_PER_PAGE=10
STREAMLIT_AUTO_REFRESH_INTERVAL=30

# Streamlit 主题配置
STREAMLIT_PRIMARY_COLOR=#FF6B6B
STREAMLIT_BACKGROUND_COLOR=#FFFFFF
STREAMLIT_SECONDARY_BACKGROUND_COLOR=#F0F2F6
STREAMLIT_TEXT_COLOR=#262730

# ==================== 性能配置 ====================
# 缓存配置
ENABLE_CACHE=true
CACHE_TTL=3600
STREAMLIT_CACHE_TTL=300

# 并发配置
MAX_CONCURRENT_REQUESTS=100

# ==================== 安全配置 ====================
# CORS 配置
CORS_ORIGINS=["*"]
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# ==================== 其他配置 ====================
# 其他可选配置项（注释掉的配置项不会被加载）

# 开发配置
# DEV_MODE=false
# DEV_API_MOCK=false

# 生产配置
# PROD_SSL_ENABLED=false
# PROD_DOMAIN=localhost

# 监控配置
# METRICS_ENABLED=true
# HEALTH_CHECK_INTERVAL=30

# 备份配置
# BACKUP_ENABLED=false
# BACKUP_DIR=./backups
